{"checksum": "220ff34b2b9a6c8a51bdf56485026088", "roots": {"bookmark_bar": {"children": [{"children": [{"date_added": "13398247362698257", "date_last_used": "0", "guid": "0ce11c07-f32e-4b28-a18f-1e12cb7a3978", "id": "7", "name": "网止导航", "show_icon": false, "source": "import_fre", "type": "url", "url": "http://dh.xiongmaoxitong.net/", "visit_count": 0}], "date_added": "13398247649432198", "date_last_used": "0", "date_modified": "0", "guid": "c64a44a4-76e6-41a6-b6fd-06f78c8869b8", "id": "6", "name": "links", "source": "import_fre", "type": "folder"}, {"date_added": "13398247362698257", "date_last_used": "0", "guid": "b0564ddb-228d-4c92-84d8-d9dcc215f851", "id": "8", "name": "网止导航", "show_icon": false, "source": "import_fre", "type": "url", "url": "http://dh.xiongmaoxitong.net/", "visit_count": 0}, {"children": [{"date_added": "13398247362698257", "date_last_used": "0", "guid": "155634fe-ceb9-4655-b2fb-01e0c74fcbeb", "id": "10", "name": "网止导航", "show_icon": false, "source": "import_fre", "type": "url", "url": "http://dh.xiongmaoxitong.net/", "visit_count": 0}], "date_added": "13398247649433099", "date_last_used": "0", "date_modified": "0", "guid": "3c13918b-2072-41ad-8455-15f8b3329fb4", "id": "9", "name": "链接", "source": "import_fre", "type": "folder"}], "date_added": "*****************", "date_last_used": "0", "date_modified": "0", "guid": "0bc5d13f-2cba-5d74-951f-3f233fe6c908", "id": "1", "name": "收藏夹栏", "source": "unknown", "type": "folder"}, "other": {"children": [{"date_added": "*****************", "date_last_used": "0", "guid": "68c35675-44f6-4301-be72-4ecc51ef0889", "id": "13", "name": "app.augmentcode.com/account/subscription", "show_icon": false, "source": "user_add", "type": "url", "url": "https://app.augmentcode.com/account/subscription", "visit_count": 1}, {"date_added": "*****************", "date_last_used": "0", "guid": "a153c89b-5388-45f0-85aa-623d29c848a6", "id": "16", "name": "百度一下，你就知道", "show_icon": false, "source": "user_add", "type": "url", "url": "https://www.baidu.com/", "visit_count": 1}], "date_added": "*****************", "date_last_used": "0", "date_modified": "*****************", "guid": "82b081ec-3dd3-529c-8475-ab6c344590dd", "id": "2", "name": "其他收藏夹", "source": "unknown", "type": "folder"}, "synced": {"children": [], "date_added": "*****************", "date_last_used": "0", "date_modified": "0", "guid": "4cf2e351-0e85-532b-bb37-df045d8f8d0f", "id": "3", "name": "移动收藏夹", "source": "unknown", "type": "folder"}}, "version": 1}