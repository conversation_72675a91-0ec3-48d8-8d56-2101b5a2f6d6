# 无限邮箱系统 (Infinite Email System)

基于 cfish22.dpdns.org 域名和 Cloudflare 邮件路由的无限邮箱生成系统。

## 🌟 核心特性

- **无限邮箱**: 基于 cfish22.dpdns.org 生成无限数量的邮箱地址
- **即时可用**: 生成的邮箱地址立即可用，无需额外配置
- **自动转发**: 所有邮件自动转发到 <EMAIL>
- **零成本部署**: 基于免费域名 + Cloudflare 免费邮件路由
- **桌面客户端**: Python开发，简洁易用的GUI界面
- **批量管理**: 支持批量生成、管理和监控邮箱
- **数据加密**: 敏感数据加密存储，安全可靠

## 🚀 快速开始

### 系统要求

- Python 3.8+
- Windows 10+ (推荐)
- 网络连接

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd infinite_email_system
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑配置文件
   notepad .env
   ```

4. **运行程序**
   ```bash
   python main.py
   ```

### 打包为exe

```bash
python build.py
```

打包完成后，可执行文件位于 `dist/InfiniteEmailSystem.exe`

## ⚙️ 配置说明

### 基本配置 (config.json)

```json
{
    "app": {
        "name": "Infinite Email System",
        "version": "1.0.0",
        "debug": false
    },
    "cloudflare": {
        "api_token": "",
        "rate_limit": 100
    },
    "freenom": {
        "max_daily_registrations": 5,
        "headless_mode": true
    },
    "email": {
        "default_forward_to": "",
        "batch_size": 10,
        "auto_cleanup": true
    }
}
```

### 环境变量 (.env)

```env
# Cloudflare配置
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here

# 邮箱配置
DEFAULT_FORWARD_EMAIL=<EMAIL>

# 调试配置
DEBUG_MODE=false
LOG_LEVEL=INFO
```

## 📖 使用指南

### 1. 生成随机域名

```python
from core.domain_manager import DomainManager

domain_manager = DomainManager()
domain = await domain_manager.generate_random_domain()
print(f"生成的域名: {domain}")
```

### 2. 生成随机邮箱

```python
from core.email_generator import EmailGenerator

email_generator = EmailGenerator()
emails = email_generator.generate_batch_emails("example.tk", 10)
print(f"生成的邮箱: {emails}")
```

### 3. 设置邮件转发

```python
from core.cloudflare_api import CloudflareEmailAPI

async with CloudflareEmailAPI(api_token) as api:
    result = await api.setup_complete_email_forwarding(
        domain="example.tk",
        destination_email="<EMAIL>"
    )
```

## 🏗️ 项目结构

```
infinite_email_system/
├── main.py                 # 主程序入口
├── config/                 # 配置管理
│   ├── settings.py         # 配置管理类
│   ├── database.py         # 数据库操作
│   └── constants.py        # 常量定义
├── core/                   # 核心业务逻辑
│   ├── domain_manager.py   # 域名管理
│   ├── email_generator.py  # 邮箱生成
│   ├── cloudflare_api.py   # Cloudflare API
│   ├── email_receiver.py   # 邮件接收
│   └── freenom_manager.py  # Freenom自动化
├── ui/                     # 用户界面
│   ├── main_window.py      # 主界面
│   ├── email_manager.py    # 邮箱管理界面
│   └── settings_window.py  # 设置界面
├── utils/                  # 工具函数
│   ├── crypto.py           # 加密工具
│   ├── logger.py           # 日志工具
│   ├── validators.py       # 数据验证
│   └── helpers.py          # 辅助函数
├── resources/              # 资源文件
├── tests/                  # 单元测试
├── requirements.txt        # 依赖包
├── build.py               # 打包脚本
└── README.md              # 说明文档
```

## 🔧 API参考

### DomainManager

```python
# 生成随机域名
domain = await domain_manager.generate_random_domain(length=8)

# 检查域名可用性
is_available = await domain_manager.check_domain_availability(domain)

# 注册域名
result = await domain_manager.register_domain(domain, user_info)
```

### EmailGenerator

```python
# 生成随机前缀
prefix = email_generator.generate_random_prefix(length=8, pattern='mixed')

# 批量生成邮箱
emails = email_generator.generate_batch_emails(domain, count=10)

# 生成邮箱变体
variations = email_generator.generate_email_variations(base_email, count=5)
```

### CloudflareEmailAPI

```python
# 设置完整邮件转发
result = await api.setup_complete_email_forwarding(domain, destination)

# 创建转发规则
rule = await api.create_catch_all_rule(zone_id, destination_email)

# 获取邮件路由统计
stats = await api.get_email_routing_stats(zone_id)
```

## 🧪 测试

运行单元测试：

```bash
python -m pytest tests/
```

运行特定测试：

```bash
python -m pytest tests/test_email_generator.py
```

## 📊 性能优化

- 使用异步操作提高并发性能
- 实施API限流避免被封禁
- 本地缓存减少重复请求
- 批量操作提高效率

## 🔒 安全考虑

- API密钥加密存储
- 本地数据库加密
- 网络传输HTTPS加密
- 输入验证防止注入攻击

## 📝 更新日志

### v1.0.0 (2024-07-30)
- 初始版本发布
- 实现核心功能模块
- 支持域名和邮箱随机生成
- 集成Cloudflare邮件路由
- 提供桌面GUI界面

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

- 本软件仅供学习和研究使用
- 请遵守相关法律法规和服务条款
- 不得用于垃圾邮件或其他违法用途
- 使用本软件产生的任何后果由用户自行承担

## 📞 技术支持

如有问题或建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 提交 [Issue](issues)
3. 查看日志文件排查问题

---

**开发者**: Augment Agent  
**版本**: v1.0.0  
**更新时间**: 2024-07-30
