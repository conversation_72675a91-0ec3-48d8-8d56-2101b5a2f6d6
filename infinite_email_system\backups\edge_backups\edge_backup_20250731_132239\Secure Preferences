{"edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "发现 Microsoft Edge 扩展。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Chrome 网上应用店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "cnlefmmeadmemmdciolhbnfeacpdfbkd": {"account_extension_type": 0, "active_permissions": {"api": ["cookies", "identity", "notifications", "storage", "tabs", "scripting"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["*://*.apclassroom.collegeboard.org/*", "*://*.app.nearpod.com/*", "*://*.app.seesaw.me/*", "*://*.atlassian.net/*", "*://*.blackboard.com/*", "*://*.blogger.com/*", "*://*.calendar.google.com/*", "*://*.chat.google.com/*", "*://*.classroom.google.com/*", "*://*.commonlit.org/*", "*://*.coursera.org/*", "*://*.educationperfect.com/*", "*://*.facebook.com/*", "*://*.fiverr.com/*", "*://*.forms.office.com/*", "*://*.github.com/*", "*://*.intercom.io/*", "*://*.keep.google.com/*", "*://*.lightning.force.com/*", "*://*.linkedin.com/*", "*://*.mail.aol.com/*", "*://*.mail.google.com/*", "*://*.medium.com/*", "*://*.messenger.com/*", "*://*.onlinechatdashboard.com/*", "*://*.outlook.live.com/*", "*://*.outlook.office.com/*", "*://*.papago.naver.com/*", "*://*.publish.buffer.com/*", "*://*.quora.com/*", "*://*.readworks.org/*", "*://*.reddit.com/*", "*://*.slack.com/*", "*://*.studio.youtube.com/*", "*://*.teams.microsoft.com/*", "*://*.translate.google.com/*", "*://*.trello.com/*", "*://*.twitch.tv/*", "*://*.twitter.com/*", "*://*.upwork.com/*", "*://*.wattpad.com/*", "*://*.web.whatsapp.com/*", "*://*.wix.com/*", "*://*.wordcounter.net/*", "*://*.wordpress.com/*", "*://*.x.com/*", "*://*.youtube.com/*", "*://*.zendesk.com/*", "*://apclassroom.collegeboard.org/*", "*://app.nearpod.com/*", "*://app.seesaw.me/*", "*://calendar.google.com/*", "*://chat.google.com/*", "*://classroom.google.com/*", "*://commonlit.org/*", "*://coursera.org/*", "*://docs.google.com/*", "*://docs.google.com/document/*", "*://educationperfect.com/*", "*://facebook.com/*", "*://fiverr.com/*", "*://forms.office.com/*", "*://github.com/*", "*://intercom.io/*", "*://keep.google.com/*", "*://linkedin.com/*", "*://mail.aol.com/*", "*://mail.google.com/*", "*://medium.com/*", "*://messenger.com/*", "*://onlinechatdashboard.com/*", "*://outlook.live.com/*", "*://outlook.office.com/*", "*://papago.naver.com/*", "*://publish.buffer.com/*", "*://quora.com/*", "*://readworks.org/*", "*://reddit.com/*", "*://studio.youtube.com/*", "*://teams.microsoft.com/*", "*://translate.google.com/*", "*://trello.com/*", "*://twitch.tv/*", "*://twitter.com/*", "*://upwork.com/*", "*://wattpad.com/*", "*://web.whatsapp.com/*", "*://wix.com/*", "*://wordcounter.net/*", "*://wordpress.com/*", "*://x.com/*", "*://youtube.com/*", "<all_urls>", "https://*.overleaf.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8193, "disable_reasons": [], "edge_last_update_check_time": "13398412548719124", "filtered_service_worker_events": {"windows.onFocusChanged": [{}], "windows.onRemoved": [{}]}, "first_install_time": "13398407471460200", "from_webstore": false, "granted_permissions": {"api": ["cookies", "identity", "notifications", "storage", "tabs", "scripting"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["*://*.apclassroom.collegeboard.org/*", "*://*.app.nearpod.com/*", "*://*.app.seesaw.me/*", "*://*.atlassian.net/*", "*://*.blackboard.com/*", "*://*.blogger.com/*", "*://*.calendar.google.com/*", "*://*.chat.google.com/*", "*://*.classroom.google.com/*", "*://*.commonlit.org/*", "*://*.coursera.org/*", "*://*.educationperfect.com/*", "*://*.facebook.com/*", "*://*.fiverr.com/*", "*://*.forms.office.com/*", "*://*.github.com/*", "*://*.intercom.io/*", "*://*.keep.google.com/*", "*://*.lightning.force.com/*", "*://*.linkedin.com/*", "*://*.mail.aol.com/*", "*://*.mail.google.com/*", "*://*.medium.com/*", "*://*.messenger.com/*", "*://*.onlinechatdashboard.com/*", "*://*.outlook.live.com/*", "*://*.outlook.office.com/*", "*://*.papago.naver.com/*", "*://*.publish.buffer.com/*", "*://*.quora.com/*", "*://*.readworks.org/*", "*://*.reddit.com/*", "*://*.slack.com/*", "*://*.studio.youtube.com/*", "*://*.teams.microsoft.com/*", "*://*.translate.google.com/*", "*://*.trello.com/*", "*://*.twitch.tv/*", "*://*.twitter.com/*", "*://*.upwork.com/*", "*://*.wattpad.com/*", "*://*.web.whatsapp.com/*", "*://*.wix.com/*", "*://*.wordcounter.net/*", "*://*.wordpress.com/*", "*://*.x.com/*", "*://*.youtube.com/*", "*://*.zendesk.com/*", "*://apclassroom.collegeboard.org/*", "*://app.nearpod.com/*", "*://app.seesaw.me/*", "*://calendar.google.com/*", "*://chat.google.com/*", "*://classroom.google.com/*", "*://commonlit.org/*", "*://coursera.org/*", "*://docs.google.com/*", "*://docs.google.com/document/*", "*://educationperfect.com/*", "*://facebook.com/*", "*://fiverr.com/*", "*://forms.office.com/*", "*://github.com/*", "*://intercom.io/*", "*://keep.google.com/*", "*://linkedin.com/*", "*://mail.aol.com/*", "*://mail.google.com/*", "*://medium.com/*", "*://messenger.com/*", "*://onlinechatdashboard.com/*", "*://outlook.live.com/*", "*://outlook.office.com/*", "*://papago.naver.com/*", "*://publish.buffer.com/*", "*://quora.com/*", "*://readworks.org/*", "*://reddit.com/*", "*://studio.youtube.com/*", "*://teams.microsoft.com/*", "*://translate.google.com/*", "*://trello.com/*", "*://twitch.tv/*", "*://twitter.com/*", "*://upwork.com/*", "*://wattpad.com/*", "*://web.whatsapp.com/*", "*://wix.com/*", "*://wordcounter.net/*", "*://wordpress.com/*", "*://x.com/*", "*://youtube.com/*", "<all_urls>", "https://*.overleaf.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398407471460200", "location": 1, "manifest": {"action": {"default_icon": "src/icon/app/icon-48.png", "default_popup": "src/popup.html", "default_title": "Grammarly"}, "background": {"service_worker": "sw.js"}, "content_scripts": [{"all_frames": true, "css": ["src/css/Grammarly-fonts.styles.css"], "exclude_globs": ["*docs.google.com*"], "exclude_matches": ["*://outlook.live.com/*", "*://*.outlook.live.com/*", "*://outlook.office.com/*", "*://*.outlook.office.com/*", "*://*.atlassian.net/*", "*://mail.google.com/*", "*://*.mail.google.com/*", "*://quora.com/*", "*://*.quora.com/*", "*://*.slack.com/*", "*://*.blackboard.com/*", "*://*.blogger.com/*", "*://publish.buffer.com/*", "*://*.publish.buffer.com/*", "*://facebook.com/*", "*://*.facebook.com/*", "*://calendar.google.com/*", "*://*.calendar.google.com/*", "*://keep.google.com/*", "*://*.keep.google.com/*", "*://intercom.io/*", "*://*.intercom.io/*", "*://linkedin.com/*", "*://*.linkedin.com/*", "*://medium.com/*", "*://*.medium.com/*", "*://messenger.com/*", "*://*.messenger.com/*", "*://teams.microsoft.com/*", "*://*.teams.microsoft.com/*", "*://translate.google.com/*", "*://*.translate.google.com/*", "*://reddit.com/*", "*://*.reddit.com/*", "*://youtube.com/*", "*://*.youtube.com/*", "*://twitter.com/*", "*://*.twitter.com/*", "*://x.com/*", "*://*.x.com/*", "*://*.lightning.force.com/*", "*://trello.com/*", "*://*.trello.com/*", "*://upwork.com/*", "*://*.upwork.com/*", "*://web.whatsapp.com/*", "*://*.web.whatsapp.com/*", "*://wix.com/*", "*://*.wix.com/*", "*://wordpress.com/*", "*://*.wordpress.com/*", "*://*.zendesk.com/*", "*://wattpad.com/*", "*://*.wattpad.com/*", "*://onlinechatdashboard.com/*", "*://*.onlinechatdashboard.com/*", "*://wordcounter.net/*", "*://*.wordcounter.net/*", "*://fiverr.com/*", "*://*.fiverr.com/*", "*://educationperfect.com/*", "*://*.educationperfect.com/*", "*://apclassroom.collegeboard.org/*", "*://*.apclassroom.collegeboard.org/*", "*://studio.youtube.com/*", "*://*.studio.youtube.com/*", "*://chat.google.com/*", "*://*.chat.google.com/*", "*://twitch.tv/*", "*://*.twitch.tv/*", "*://papago.naver.com/*", "*://*.papago.naver.com/*", "*://readworks.org/*", "*://*.readworks.org/*", "*://app.nearpod.com/*", "*://*.app.nearpod.com/*", "*://mail.aol.com/*", "*://*.mail.aol.com/*", "*://github.com/*", "*://*.github.com/*", "*://coursera.org/*", "*://*.coursera.org/*", "*://commonlit.org/*", "*://*.commonlit.org/*", "*://classroom.google.com/*", "*://*.classroom.google.com/*", "*://app.seesaw.me/*", "*://*.app.seesaw.me/*", "*://forms.office.com/*", "*://*.forms.office.com/*", "*://docs.google.com/document/*"], "js": ["src/js/Grammarly-check.styles.js", "src/js/Grammarly-check.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_idle"}, {"all_frames": true, "css": ["src/css/Grammarly-fonts.styles.css"], "exclude_globs": ["*docs.google.com*"], "js": ["src/js/Grammarly.styles.js", "src/js/Grammarly.js"], "match_about_blank": true, "matches": ["*://*.atlassian.net/*", "*://mail.google.com/*", "*://*.mail.google.com/*", "*://quora.com/*", "*://*.quora.com/*", "*://*.slack.com/*", "*://*.blackboard.com/*", "*://*.blogger.com/*", "*://publish.buffer.com/*", "*://*.publish.buffer.com/*", "*://facebook.com/*", "*://*.facebook.com/*", "*://calendar.google.com/*", "*://*.calendar.google.com/*", "*://keep.google.com/*", "*://*.keep.google.com/*", "*://intercom.io/*", "*://*.intercom.io/*", "*://linkedin.com/*", "*://*.linkedin.com/*", "*://medium.com/*", "*://*.medium.com/*", "*://messenger.com/*", "*://*.messenger.com/*", "*://teams.microsoft.com/*", "*://*.teams.microsoft.com/*", "*://translate.google.com/*", "*://*.translate.google.com/*", "*://reddit.com/*", "*://*.reddit.com/*", "*://youtube.com/*", "*://*.youtube.com/*", "*://twitter.com/*", "*://*.twitter.com/*", "*://x.com/*", "*://*.x.com/*", "*://*.lightning.force.com/*", "*://trello.com/*", "*://*.trello.com/*", "*://upwork.com/*", "*://*.upwork.com/*", "*://web.whatsapp.com/*", "*://*.web.whatsapp.com/*", "*://wix.com/*", "*://*.wix.com/*", "*://wordpress.com/*", "*://*.wordpress.com/*", "*://*.zendesk.com/*", "*://wattpad.com/*", "*://*.wattpad.com/*", "*://onlinechatdashboard.com/*", "*://*.onlinechatdashboard.com/*", "*://wordcounter.net/*", "*://*.wordcounter.net/*", "*://fiverr.com/*", "*://*.fiverr.com/*", "*://educationperfect.com/*", "*://*.educationperfect.com/*", "*://apclassroom.collegeboard.org/*", "*://*.apclassroom.collegeboard.org/*", "*://studio.youtube.com/*", "*://*.studio.youtube.com/*", "*://chat.google.com/*", "*://*.chat.google.com/*", "*://twitch.tv/*", "*://*.twitch.tv/*", "*://papago.naver.com/*", "*://*.papago.naver.com/*", "*://readworks.org/*", "*://*.readworks.org/*", "*://app.nearpod.com/*", "*://*.app.nearpod.com/*", "*://mail.aol.com/*", "*://*.mail.aol.com/*", "*://github.com/*", "*://*.github.com/*", "*://coursera.org/*", "*://*.coursera.org/*", "*://commonlit.org/*", "*://*.commonlit.org/*", "*://classroom.google.com/*", "*://*.classroom.google.com/*", "*://app.seesaw.me/*", "*://*.app.seesaw.me/*", "*://forms.office.com/*", "*://*.forms.office.com/*"], "run_at": "document_idle"}, {"all_frames": true, "css": ["src/css/Grammarly-fonts.styles.css"], "exclude_globs": ["*docs.google.com*"], "js": ["src/js/Grammarly.styles.js", "src/js/Grammarly.js"], "match_about_blank": false, "matches": ["*://outlook.live.com/*", "*://*.outlook.live.com/*", "*://outlook.office.com/*", "*://*.outlook.office.com/*"], "run_at": "document_idle"}, {"all_frames": false, "css": ["src/css/Grammarly-fonts.styles.css"], "js": ["src/js/Grammarly-gDocs.styles.js", "src/js/Grammarly-gDocs.js"], "matches": ["*://docs.google.com/document/*"], "run_at": "document_idle"}, {"all_frames": false, "js": ["src/js/Grammarly-gDocsEarlyInjector.js"], "matches": ["*://docs.google.com/document/*"], "run_at": "document_start"}, {"all_frames": true, "exclude_matches": ["*://docs.google.com/document/*"], "js": ["src/js/Grammarly-gDocsIframeCs.js"], "match_about_blank": true, "matches": ["*://docs.google.com/*"], "run_at": "document_idle"}, {"all_frames": false, "js": ["src/js/Grammarly-overleafStartContentScript.js"], "matches": ["https://*.overleaf.com/*"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "default-src 'none'; font-src 'self' data:; img-src * data: blob:; media-src https://assets.extension.grammarly.com; script-src 'self'; style-src 'self' 'unsafe-inline'; frame-src 'self' https://applet-bundles.grammarly.net https://d3ttvzt45fz9bg.cloudfront.net; connect-src https://auth.grammarly.com https://assets.grammarly.com/ https://data.grammarly.com https://capi.grammarly.com https://dox.grammarly.com https://institution.grammarly.com wss://capi.grammarly.com https://gnar.grammarly.com https://in.grammarly.com https://f-log-extension.grammarly.io https://f-log-editor.grammarly.io https://f-log-inkwell.grammarly.io https://config.extension.grammarly.com https://chipmunk.grammarly.com https://treatment.grammarly.com https://gates.grammarly.com https://goldengate.grammarly.com https://femetrics.grammarly.io https://extension.femetrics.grammarly.io https://inkwell.femetrics.grammarly.io https://rwsgfy.grammarly.com https://assets.extension.grammarly.com https://api.iterable.com https://skills.grammarly.com https://skills-proxy.grammarly.com https://gateway.grammarly.com ;  report-uri https://f-log-extension.grammarly.io/logv2; "}, "description": "Improve your writing with all-in-one assistance—including generative AI, grammar check, and more.", "externally_connectable": {"matches": ["https://*.grammarly.com/*"]}, "host_permissions": ["http://*/*", "https://*/*"], "icons": {"128": "src/icon/app/icon-128.png", "16": "src/icon/app/icon-16.png", "48": "src/icon/app/icon-48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7qxHBdy17inyypagVy4qvbPuYrEMVd/P4e+dRAceSjpetXLLXUXRq0SpW65Q7KOkUwqGMQCTL6iQLCaV1g7IPy4nT7DfMI4HXrWsuVaN8qKmexwYGwwQC2K0UguVb8T3tzEgIGfRRkhfMQXYXXzIe8VW2XA8aRBCNWIPXxM7F/lCEi0CoRxpFhFs448SNea05eEoT/RthitHORYleZf8cE5BDk0B1wh23ScmeixIN1Jd3Oriqz+0oVcrMaBatF30H23S2NtZh2z2wBVetpXMoAwoWKm3gLvH1M6k/WrOC9RHLMZFRrTqCjl8SGmvaqdT+/6IX/5OdUBFAGlI0HiKbQIDAQAB", "manifest_version": 3, "minimum_chrome_version": "88", "name": "Grammarly: AI Writing and Grammar Checker App", "optional_permissions": ["clipboardRead"], "permissions": ["scripting", "tabs", "notifications", "cookies", "identity", "storage"], "storage": {"managed_schema": "src/schema.json"}, "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "14.1114.0", "web_accessible_resources": [{"matches": ["http://*/*", "https://*/*"], "resources": ["src/fonts/*.woff", "src/fonts/*.woff2", "src/images/*.png", "src/images/*.svg", "src/images/*.jpg", "src/images/*.gif", "src/js/*.js", "src/css/*.css", "src/inkwell/index.html", "src/inkwell/assets/*.js"]}]}, "needs_sync": true, "path": "cnlefmmeadmemmdciolhbnfeacpdfbkd\\14.1114.0_0", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "14.1114.0"}, "serviceworkerevents": ["cookies.onChanged", "permissions.onAdded", "permissions.onRemoved", "runtime.onUpdateAvailable", "storage.onChanged", "tabs.onActivated", "tabs.onUpdated"], "storage_session_access_level": 2, "uninstall_url": "https://www.grammarly.com/extension-uninstall?domain=settings&utm_medium=internal&utm_campaign=extensionUninstall&extensionName=edge&extensionVersion=14.1114.0", "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283203108", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283203108", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283204221", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283204221", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "geiinlhabolacmdgdkbkppfmijlemjep": {"active_permissions": {"api": ["clipboardRead", "clipboardWrite", "metricsPrivate", "resourcesPrivate", "tracingPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283203432", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283203432", "location": 5, "manifest": {"content_security_policy": "default-src 'none'; script-src 'self' chrome://resources; style-src 'unsafe-inline'; img-src data:; base-uri 'self'; form-action 'none'; frame-ancestors 'none'", "description": "Collections extension", "display_in_launcher": false, "display_in_new_tab_page": false, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArFxU7em9D10ThRDSf/OsvEhg72GZKONSkRfqL1J3GUWDztAWQI+QkIBV2UhTEUkZJgdfCTmMmAqRk1C5YjlNQYRBAVyhRWHNuOXl84XJpIv+v3K7AYGz3jbJkyz7Pb3ee8dTj1/wmk7xmlFYYR5Y9gjYYi8eUTdn47GGJ0TVkW6hPOJcTF0Bid7yoavO2CJIvAdQ1oYbXAryKG0Dq/lfF4Fr9Oyt3NsepMaN4j6gNdwhMS2sLGj+KodjM1Jb8o4ixPzishDKQwlcbW7VD6T66DF6PubqycIKmAOhQNTocUQ2eJQWvqd6Z2xD//IhA4u0vVQWHXy5PBsMidzdstrm5QIDAQAB", "manifest_version": 2, "name": "Edge Collections", "permissions": ["chrome://resources/", "collectionsPrivate", "metricsPrivate", "resourcesPrivate", "clipboardRead", "clipboardWrite", "tracingPrivate"], "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\edge_collections", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1048713, "disable_reasons": [*********], "edge_last_update_check_time": "*****************", "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["alarms", "storage", "unlimitedStorage", "offscreen"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 6, "manifest": {"author": {"email": "<EMAIL>"}, "background": {"service_worker": "service_worker_bin_prod.js"}, "content_capabilities": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"], "permissions": ["clipboardRead", "clipboardWrite", "unlimitedStorage"]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "current_locale": "zh_CN", "default_locale": "en_US", "description": "编辑、创建和查看文档、电子表格和演示文稿，无需连接互联网。", "externally_connectable": {"matches": ["https://docs.google.com/*", "https://drive.google.com/*", "https://drive-autopush.corp.google.com/*", "https://drive-daily-0.corp.google.com/*", "https://drive-daily-1.corp.google.com/*", "https://drive-daily-2.corp.google.com/*", "https://drive-daily-3.corp.google.com/*", "https://drive-daily-4.corp.google.com/*", "https://drive-daily-5.corp.google.com/*", "https://drive-daily-6.corp.google.com/*", "https://drive-preprod.corp.google.com/*", "https://drive-staging.corp.google.com/*"]}, "host_permissions": ["https://docs.google.com/*", "https://drive.google.com/*"], "icons": {"128": "128.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnF7RGLAxIon0/XeNZ4MLdP3DMkoORzEAKVg0sb89JpA/W2osTHr91Wqwdc9lW0mFcSpCYS9Y3e7cUMFo/M2ETASIuZncMiUzX2/0rrWtGQ3UuEj3KSe5PdaVZfisyJw/FebvHwirEWrhqcgzVUj9fL9YjE0G45d1zMKcc1umKvLqPyTznNuKBZ9GJREdGLRJCBmUgCkI8iwtwC+QZTUppmaD50/ksnEUXv+QkgGN07/KoNA5oAgo49Jf1XBoMv4QXtVZQlBYZl84zAsI82hb63a6Gu29U/4qMWDdI7+3Ne5TRvo6Zi3EI4M2NQNplJhik105qrz+eTLJJxvf4slrWwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "88", "name": "Google 文档的离线功能", "permissions": ["alarms", "storage", "unlimitedStorage", "offscreen"], "storage": {"managed_schema": "dasherSettingSchema.json"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.94.1", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["page_embed_script.js"]}]}, "path": "ghbmnnjooekpmoecnnnilnnbdlolhkhi\\1.94.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": true, "was_installed_by_oem": false, "withholding_permissions": false}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "n", "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283202809", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283202809", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "发现 Microsoft Edge 扩展。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283203951", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283203951", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "fullscreen", "metricsPrivate", "storage"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "jdiccldimpdaibmpdkjnbmckianbfold": {"active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "13398247283204788", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283204788", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "jmjflgjpcpepeafmmgdpfkogkghcpiha": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 8321, "disable_reasons": [], "edge_last_update_check_time": "*****************", "events": [], "first_install_time": "*****************", "from_webstore": false, "granted_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"content_scripts": [{"js": ["content.js"], "matches": ["https://chrome.google.com/webstore/*"]}, {"js": ["content_new.js"], "matches": ["https://chromewebstore.google.com/*"]}], "description": "Edge relevant text changes on select websites to improve user experience and precisely surfaces the action they want to take.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu06p2Mjoy6yJDUUjCe8Hnqvtmjll73XqcbylxFZZWe+MCEAEK+1D0Nxrp0+IuWJL02CU3jbuR5KrJYoezA36M1oSGY5lIF/9NhXWEx5GrosxcBjxqEsdWv/eDoOOEbIvIO0ziMv7T1SUnmAA07wwq8DXWYuwlkZU/PA0Mxx0aNZ5+QyMfYqRmMpwxkwPG8gyU7kmacxgCY1v7PmmZo1vSIEOBYrxl064w5Q6s/dpalSJM9qeRnvRMLsszGY/J2bjQ1F0O2JfIlBjCOUg/89+U8ZJ1mObOFrKO4um8QnenXtH0WGmsvb5qBNrvbWNPuFgr2+w5JYlpSQ+O8zUCb8QZwIDAQAB", "manifest_version": 3, "name": "Edge relevant text changes", "update_url": "https://edge.microsoft.com/extensionwebstorebase/v1/crx", "version": "1.2.1"}, "path": "jmjflgjpcpepeafmmgdpfkogkghcpiha\\1.2.1_0", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": true, "was_installed_by_oem": false}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kieiaeokhmlbffedbdfgbdcnhgpcckkn": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kmendfapggjehodndflmmgagdbamhnfd": {"active_permissions": {"api": ["cryptotokenPrivate", "externally_connectable.all_urls", "tabs"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283205133", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283205133", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["util.js", "b64.js", "cbor.js", "sha256.js", "timer.js", "countdown.js", "countdowntimer.js", "devicestatuscodes.js", "approvedorigins.js", "errorcodes.js", "webrequest.js", "messagetypes.js", "factoryregistry.js", "requesthelper.js", "asn1.js", "enroller.js", "requestqueue.js", "signer.js", "origincheck.js", "textfetcher.js", "appid.js", "watchdog.js", "logging.js", "webrequestsender.js", "window-timer.js", "cryptotokenorigincheck.js", "cryptotokenapprovedorigins.js", "inherits.js", "individualattest.js", "googlecorpindividualattest.js", "cryptotokenbackground.js"]}, "description": "CryptoToken Component Extension", "externally_connectable": {"ids": ["fjajfjhkeibgmiggdfehjplbhmfkialk"], "matches": ["https://*/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB", "manifest_version": 2, "name": "CryptoTokenExtension", "permissions": ["cryptotokenPrivate", "externally_connectable.all_urls", "tabs", "https://*/*", "http://*/*"], "version": "0.9.74"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\cryptotoken", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283202322", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283202322", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "contentSettings", "metricsPrivate", "edgeLearningToolsPrivate", "resourcesPrivate", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "first_install_time": "13398247283204440", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398247283204440", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\92.0.902.67\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_bookmark": false, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.teams.microsoft.com/*", "https://*.meet.teams.live.com/*", "https://*.meet.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}}}, "media": {"cdm": {"origin_data": {}}}, "prefs": {"preference_reset_time": "*****************"}, "protection": {"macs": {"browser": {"show_home_button": "85AC3D2C93FEF9790863922A7C6551257F877F22B5C58BC49ACD9428ABE5CE5D"}, "default_search_provider_data": {"template_url_data": "42A28BB07685E0601ACB60E4C2548AE375E48AA71C427FA355579851EBC7B66A"}, "edge": {"services": {"account_id": "E33781877887D2149FA45DB4E61E147A24A48310EDF6D7553F3E3662CE29148E", "last_account_id": "52EF2FB3673ED49EFD90B732B756AF57AED0966D6AF4E0DCBCC7507D35852516", "last_username": "923E03F4DE71A4720A7A6272BCA4612847BFC2AFACEBCC25EA012950561916AD"}}, "enterprise_signin": {"policy_recovery_token": "35968D9EEF9D1627005827A7AB7D91098EBBE1DDB25403DA49C5539D32FA3675"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "0EFD90AD22A3C80FDEBD1EB056446E588505FBE15F2F0D8247E3AFE136AD213E", "cjneempfhkonkkbcmnfdibgobmhbagaj": "FCC0F51C62BE91FDE5FEB2548477FC295651A127F9B67583FC4F9B86F4411E02", "cnlefmmeadmemmdciolhbnfeacpdfbkd": "672EA905E11C20EDDC2FD81DB3340E7CCA3453BF003CF9D90EBA8EFB689E56BD", "dcaajljecejllikfgbhjdgeognacjkkp": "707FE39CD2945BD66F209F6AA5106BC45E60D3BE45BBB1CFC3132C9E5ADF1300", "dgiklkfkllikcanfonkcabmbdfmgleag": "D96FDFEB137F5D921F7A910C5ECE4621A1A464D7AEE81845679FDFC4DA5E3353", "ehlmnljdoejdahfjdfobmpfancoibmig": "F182751BA4560D76300B3F59353A1892177E2D0F47C12A53B3C3C9940065B732", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "AF7F37EBE8DEF680518C3A19EB57B57BD163856C7F64E9D46CB88BE732F99C09", "fjngpfnaikknjdhkckmncgicobbkcnle": "6333366F451097BBEC3C3B6204CF5B8B3AB20ED92A4B008E94E5536DD05B1178", "gbihlnbpmfkodghomcinpblknjhneknc": "A68BDA37D9D71D4829E827025DFACC65095DC9D22EF32C96A906B7A524CF55FD", "gbmoeijgfngecijpcnbooedokgafmmji": "5E7C10990E9AE9CC209AFE24D52F0B23A24934375AC40EEDDFF1825101BF0A55", "gcinnojdebelpnodghnoicmcdmamjoch": "21E115A6D9E69F17C6928681F0BFC92D07A67912AA73B17D69DAE21E08640AFC", "gecfnmoodchdkebjjffmdcmeghkflpib": "58E10D32C12413EDFBA95E4486E6D1F990219A1E0F45330CB901FBE1A99B2683", "geiinlhabolacmdgdkbkppfmijlemjep": "61C274A14B0BC1E607DF09C7E32488E2021DDBFC8C6EE182BACDF6D31ACC7AA7", "ghbmnnjooekpmoecnnnilnnbdlolhkhi": "AB98D6495B9CF6F44972B62D841306D9FADDFF421384CFB534AE8820F21FBB63", "hfmgbegjielnmfghmoohgmplnpeehike": "79909DB9ED139309FA184AB98237CDFEE567E5919DEDA247BA6755F8B2447AAC", "iglcjdemknebjbklcgkfaebgojjphkec": "08FB3FD2239C505A8F9F6993515BB69E8DAAC9E4EB53AECD644CE6242D8C6D96", "ihmafllikibpmigkcoadcmckbfhibefp": "D7564215A7AA6D014BD28F347EA5E70FE509DAEA85DF0BCE25725D1F9CCFD570", "jbleckejnaboogigodiafflhkajdmpcl": "2793DAC4AB123BCF0546434EDFE71B72C6B5FDD06BEE75432F4B497C5971EF1F", "jdiccldimpdaibmpdkjnbmckianbfold": "C61A336D03C079703F4ECB3ADC403343ECD385278179C3A252E823C388734130", "jmjflgjpcpepeafmmgdpfkogkghcpiha": "533D02BFD5583C0A2D9F158EC02D7B81F22FF0FFB46477E1B4D1F9E8A00C2086", "kfihiegbjaloebkmglnjnljoljgkkchm": "15A3B89CC1673675B43F5FAA65924B6F3067D26500964B2F963F1BE051EEDE9F", "kieiaeokhmlbffedbdfgbdcnhgpcckkn": "E08C58FDFA8DFFE0FD7824D64774CB1696734A6C5014366098B9114A5AD567E1", "kmendfapggjehodndflmmgagdbamhnfd": "E1E1FFE46F313733030E047AE406EA07E1606B78FBB79A1F21FE56046C93DABA", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "BFD3ABD92FDD48EEC1DF850C24584FD199B082DE6022AC60E93EDF84C3891BA0", "ncbjelpjchkpbikbpkcchkhkblodoama": "6BB640CBC813C8F78705477899E69215B3FADF7FF96C141DAAC43FAD203DF1D6", "nkbndigcebkoaejohleckhekfmcecfja": "8E4ED3D045B6C5C03EE8F06B83F17496AA8B0967FBDEB1EA3C6D4B11F6E647F6", "nkeimhogjdpnpccoofpliimaahmaaome": "86FE150653E59C785E3B8E113728A8F2121CFAFED18CEF13A5FA4F70D42810DD", "ofefcgjbeghpigppfmkologfjadafddi": "57BC96F7322AD0DA82B32E54A6370798CB096750D044B864E7054CD23DF48F68"}, "ui": {"developer_mode": "857740A3CCCE88762D9972748DDEE8B254E1158B778B0334E6091FB939D24646"}}, "google": {"services": {"last_signed_in_username": "904D3A532BD6F6D9EB9B91062897A4793D5C90BF1756C38A280882CE7B5E2D4B"}}, "homepage": "89BD6C66801F6631B68D0DD715430D0C720E6A40D90329D3BCACCBC8F9346C95", "homepage_is_newtabpage": "BE5F711D351E98B2C345E1E8F5C44B313C1C5C4E5FC135DF22C135E9A4CDE06E", "media": {"cdm": {"origin_data": "1AA50D5AA72C1CE6EEC34C9408316DF4474C6A893A662DD1E5A6418474FEA0ED"}, "storage_id_salt": "303738E774077A3AEABC52941DDFB30AA79A6A411627E3B9AFC5E629117C3411"}, "pinned_tabs": "88C57E12410ACF03F32904444BA61F6CDED517A7784B9D9AA4618551E3399969", "prefs": {"preference_reset_time": "EBC5B8D51473D8312C373B8F8F7AB35FB6692B66242B0204EC69AFC4EC7E1BB0"}, "safebrowsing": {"incidents_sent": "7334474907826AF6A7A83CE76A9DD56463B48737EBA9C4BF042BCDD645800450"}, "search_provider_overrides": "BDE1D0E7C5D9F86F53A1E0E6DEBE1FAA522AC075BBDED21DEBF312768D9C594E", "session": {"restore_on_startup": "0D7F5EFD9CFA739563759AC156FFE9B3443862BA5A8F492C9A13433B346B1992", "startup_urls": "2A9D8089B116D064D1D9754AAAB0B00913F6CDD52021AE6AFB752D06C1B23785"}}, "super_mac": "8089FA72DE218DD357FB0408456F4BFE1A839D53E9A23E90FAAE6CFDCA35F925"}}